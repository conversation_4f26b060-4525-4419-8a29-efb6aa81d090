// components/FloatingMusicBall.js
import { useWindowDimensions } from '@jgl/biz-func';
import { useCallback, useMemo, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import type { FloatingAudioBallProps } from './types';

const BALL_SIZE = 60;

const BALL_RADIUS = BALL_SIZE / 2;

export const FloatingAudioBall = (props: FloatingAudioBallProps) => {
  const { audioInfo, onHide } = props;

  const [isExpanded, setIsExpanded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);

  const translateX = useSharedValue(10);
  const translateY = useSharedValue(300);
  const context = useSharedValue({ x: 0, y: 0 });

  const { width: screenWidth } = useWindowDimensions();

  const expandedWidth = useMemo(() => {
    return screenWidth * 0.8;
  }, [screenWidth]);

  const toggleExpand = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  const panGesture = useMemo(() => {
    return Gesture.Pan()
      .onStart(() => {
        context.value = { x: translateX.value, y: translateY.value };
      })
      .onUpdate((event) => {
        translateX.value = context.value.x + event.translationX;
        translateY.value = context.value.y + event.translationY;
      })
      .onEnd(() => {
        const finalWidth = isExpanded ? expandedWidth : BALL_SIZE;
        if (translateX.value > screenWidth / 2 - finalWidth / 2) {
          translateX.value = withSpring(screenWidth - finalWidth - 10);
        } else {
          translateX.value = withSpring(10);
        }
      });
  }, [context, expandedWidth, isExpanded, screenWidth, translateX, translateY]);

  const tapGesture = useMemo(() => {
    return Gesture.Tap().onEnd(() => {
      runOnJS(toggleExpand)();
    });
  }, [toggleExpand]);

  /** 组合手势：如果拖拽失败（即被识别为点击），则执行点击手势 */
  const composedGesture = useMemo(() => {
    return Gesture.Race(panGesture, tapGesture);
  }, [panGesture, tapGesture]);

  const animatedStyle = useAnimatedStyle(() => {
    const width = withTiming(isExpanded ? expandedWidth : BALL_SIZE, {
      duration: 250,
    });
    return {
      width,
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
    };
  }, [expandedWidth]);

  const collapsedOpacity = useAnimatedStyle(() => ({
    opacity: withTiming(isExpanded ? 0 : 1),
  }));

  const expandedOpacity = useAnimatedStyle(() => ({
    opacity: withTiming(isExpanded ? 1 : 0),
  }));

  const handleClose = () => {
    if (isExpanded) {
      setIsExpanded(false);
    } else {
      onHide?.(); // 只有在收起状态下，按关闭才隐藏整个球
    }
  };

  return (
    <GestureDetector gesture={composedGesture}>
      <Animated.View style={[styles.container, animatedStyle]}>
        {/* 收起状态 */}
        <Animated.View
          style={[styles.content, styles.collapsedContent, collapsedOpacity]}
        >
          <Text style={styles.icon}>🎵</Text>
        </Animated.View>

        {/* 展开状态 */}
        <Animated.View
          style={[styles.content, styles.expandedContent, expandedOpacity]}
        >
          <Text style={styles.title} numberOfLines={1}>
            {audioInfo?.title}
          </Text>
          <View style={styles.controls}>
            <TouchableOpacity onPress={() => setIsPlaying((p) => !p)}>
              <Text style={styles.icon}>{isPlaying ? '⏸️' : '▶️'}</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleClose}>
              <Text style={styles.icon}>❌</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Animated.View>
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    height: BALL_SIZE,
    borderRadius: BALL_RADIUS,
    backgroundColor: 'rgba(0,0,0,0.7)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  content: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  collapsedContent: {},
  expandedContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  icon: {
    fontSize: 24,
    color: 'white',
  },
  title: {
    flex: 1,
    color: 'white',
    fontSize: 16,
    marginRight: 10,
  },
  controls: {
    flexDirection: 'row',
    gap: 15, // 使用 gap 属性简化间距
  },
});
