import type { PropsWithChildren } from 'react';
import { useMemo, useState } from 'react';
import { FloatingAudioBallContext } from './context';
import type { FloatingBallAudio, FloatingBallContextValue } from './types';

type Props = PropsWithChildren;

export const FloatingBallProvider = (props: Props) => {
  const { children } = props;

  const [isVisible, setIsVisible] = useState(false);
  const [audioInfo, setAudioInfo] = useState<FloatingBallAudio>();

  const show = (info?: FloatingBallAudio) => {
    setAudioInfo(info);
    setIsVisible(true);
  };

  const hide = () => {
    setIsVisible(false);
  };

  const value = useMemo((): FloatingBallContextValue => {
    return {
      isVisible,
      audioInfo,
      show,
      hide,
      setAudioInfo,
    };
  }, [audioInfo, isVisible]);

  return (
    <FloatingAudioBallContext.Provider value={value}>
      {children}
    </FloatingAudioBallContext.Provider>
  );
};
